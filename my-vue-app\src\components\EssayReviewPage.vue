<template>
  <div class="essay-review-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 返回按钮和标题 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <img :src="arrowLeftIcon" alt="返回" />
      </div>
      <div class="page-title">详情</div>
    </div>

    <!-- 标签导航 -->
    <div class="tab-navigation">
      <div class="tab-item" :class="{ active: activeTab === 'requirements' }" @click="setActiveTab('requirements')">
        作文要求
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'review' }" @click="setActiveTab('review')">
        <div class="tab-content">
          <div class="tab-indicator"></div>
          作文点评
        </div>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'report' }" @click="setActiveTab('report')">
        作文报告
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'polish' }" @click="setActiveTab('polish')">
        润色范文
      </div>
    </div>

    <!-- 作文基本信息 -->
    <div class="essay-info">
      <div class="info-row">
        <span class="info-label">第一单元</span>
        <span class="info-label">单元作文</span>
        <span class="info-label">全命题</span>
        <span class="essay-title">我的植物朋友</span>
        <div class="score-section">
          <span class="score">28</span>
          <span class="score-unit">分</span>
        </div>
      </div>
    </div>

    <!-- 点评标题 -->
    <div class="section-title">点评</div>

    <!-- 评分维度 -->
    <div class="rating-sections">
      <!-- 思想与中心 -->
      <div class="rating-item">
        <div class="rating-header">
          <span class="rating-label">思想与中心</span>
          <div class="star-rating">
            <div class="star" v-for="n in 4" :key="n">★</div>
          </div>
        </div>
        <div class="rating-comment-label">评语</div>
        <div class="rating-comment">
          文章紧扣'未来的城市生活'主题，通过多个方面描绘了未来城市的科技发展和生活变化，中心思想明确。
        </div>
      </div>

      <!-- 内容 -->
      <div class="rating-item">
        <div class="rating-header">
          <span class="rating-label">内容</span>
          <div class="star-rating">
            <div class="star" v-for="n in 4" :key="n">★</div>
          </div>
        </div>
        <div class="rating-comment-label">评语</div>
        <div class="rating-comment">
          内容具体详细，涵盖了建筑、交通、环保、教育和医疗等多个领域，展现了未来城市生活的全面图景。
        </div>
      </div>

      <!-- 结构 -->
      <div class="rating-item">
        <div class="rating-header">
          <span class="rating-label">结构</span>
          <div class="star-rating">
            <div class="star" v-for="n in 4" :key="n">★</div>
          </div>
        </div>
        <div class="rating-comment-label">评语</div>
        <div class="rating-comment">
          文章结构较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。
        </div>
      </div>

      <!-- 语言 -->
      <div class="rating-item">
        <div class="rating-header">
          <span class="rating-label">语言</span>
          <div class="star-rating">
            <div class="star" v-for="n in 4" :key="n">★</div>
          </div>
        </div>
        <div class="rating-comment-label">评语</div>
        <div class="rating-comment">语句通顺</div>
      </div>
    </div>

    <!-- 总结评语 -->
    <div class="summary-section">
      <div class="summary-title">总结评语</div>
      <div class="summary-content">
        本文围绕'未来的城市生活'这一主题，从建筑、交通、环保、教育和医疗等多个方面展开，描绘了一个科技高度发达的未来城市图景。文章内容具体，涵盖了多个领域，展现了未来城市生活的全面变化。结构上，文章层次较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。语言方面，文章语句基本通顺，但存在个别错别字和标点符号使用不当的问题，修辞手法的运用也较为有限。总体而言，文章紧扣主题，中心思想明确，内容具体，结构较为清晰，语言基本流畅，但仍有提升空间。
      </div>
    </div>

    <!-- 不足之处标题 -->
    <div class="section-title">不足之处</div>

    <!-- 字数不足 -->
    <div class="deficiency-item">
      <div class="deficiency-header">
        <span class="deficiency-title">字数不足</span>
        <span class="deficiency-penalty">扣2分</span>
      </div>
      <div class="deficiency-detail">
        <span class="detail-label">总字数</span>
        <span class="detail-value">100字</span>
      </div>
    </div>

    <!-- 错别字 -->
    <div class="deficiency-item">
      <div class="deficiency-header">
        <span class="deficiency-title">错别字</span>
        <span class="deficiency-count">共10处</span>
      </div>
      <div class="error-list">
        <div class="error-row">
          <span class="error-item">1.棋中（其中）</span>
          <span class="error-item">2.坐号（座号）</span>
          <span class="error-item">3.坐号（座号）</span>
          <span class="error-item">4.坐号（座号）</span>
        </div>
        <div class="error-row">
          <span class="error-item">5.棋中（其中）</span>
          <span class="error-item">6.坐号（座号）</span>
          <span class="error-item">7.坐号（座号）</span>
          <span class="error-item">8.坐号（座号）</span>
        </div>
        <div class="error-row">
          <span class="error-item">9.棋中（其中）</span>
          <span class="error-item">10.坐号（座号）</span>
        </div>
      </div>
    </div>

    <!-- 滥用拼音 -->
    <div class="deficiency-item">
      <div class="deficiency-header">
        <span class="deficiency-title">滥用拼音</span>
        <span class="deficiency-count">共10处</span>
      </div>
      <div class="error-list">
        <div class="error-row">
          <span class="error-item">1.qi（其）</span>
          <span class="error-item">2.zuo（座）</span>
          <span class="error-item">3.zuo（座）</span>
          <span class="error-item">4.zuo（座）</span>
        </div>
        <div class="error-row">
          <span class="error-item">5.zuo（座）</span>
          <span class="error-item">6.zuo（座）</span>
          <span class="error-item">7.zuo（座）</span>
          <span class="error-item">8.zuo（座）</span>
        </div>
        <div class="error-row">
          <span class="error-item">9.zuo（座）</span>
          <span class="error-item">10.zuo（座）</span>
        </div>
      </div>
    </div>

    <!-- 最后批改时间 -->
    <div class="last-modified">
      最后批改时间：2025.07.16  15:46
    </div>
  </div>
</template>

<script>
import arrowLeftIcon from '../assets/images/arrow-left.svg'

export default {
  name: 'EssayReviewPage',
  emits: ['back'],
  data() {
    return {
      arrowLeftIcon,
      activeTab: 'review'
    }
  },
  methods: {
    goBack() {
      this.$emit('back')
    },
    setActiveTab(tab) {
      this.activeTab = tab
      // 这里可以添加切换标签页的逻辑
    }
  }
}
</script>

<style scoped>
.essay-review-page {
  width: 375px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
  min-height: 100vh;
}

/* 状态栏样式 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) { height: 3.4px; }
.bar:nth-child(2) { height: 5.53px; }
.bar:nth-child(3) { height: 8.51px; }
.bar:nth-child(4) { height: 10.21px; }

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 头部样式 */
.header {
  display: flex;
  align-items: center;
  padding: 8px 24px;
  position: relative;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  font-weight: 400;
}

/* 标签导航样式 */
.tab-navigation {
  display: flex;
  background: transparent;
  padding: 0 23px;
  height: 40px;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  height: 40px;
}

.tab-item.active .tab-content {
  position: relative;
}

.tab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 11px;
  line-height: 18px;
  color: #636AE8;
  font-weight: 700;
}

.tab-item:not(.active) {
  font-size: 11px;
  line-height: 18px;
  color: #565E6C;
  font-weight: 400;
}

.tab-indicator {
  width: 60px;
  height: 4px;
  background: #636AE8;
  border-radius: 2px;
  position: absolute;
  bottom: 0;
}

/* 作文基本信息样式 */
.essay-info {
  background: #F8F9FA;
  border: 1px solid #BCC1CA;
  padding: 11px 17px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.info-label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.essay-title {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-left: auto;
  margin-right: 80px;
}

.score-section {
  position: absolute;
  right: 0;
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.score {
  font-size: 24px;
  line-height: 36px;
  color: #DE3B40;
  font-weight: 400;
}

.score-unit {
  font-size: 12px;
  line-height: 20px;
  color: #DE3B40;
  font-weight: 400;
}

/* 章节标题样式 */
.section-title {
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
  font-weight: 400;
  padding: 8px 17px;
  margin-top: 10px;
}

/* 评分维度样式 */
.rating-sections {
  padding: 0 0 20px 0;
}

.rating-item {
  background: #F8F9FA;
  border: 1px solid #BCC1CA;
  margin-bottom: 10px;
  padding: 11px 17px;
}

.rating-header {
  display: flex;
  align-items: center;
  gap: 41px;
  margin-bottom: 26px;
}

.rating-label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  width: 55px;
}

.star-rating {
  display: flex;
  gap: 0;
}

.star {
  width: 18px;
  height: 18px;
  color: #EFB034;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rating-comment-label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-bottom: 0;
}

.rating-comment {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-left: 63px;
  margin-top: 0;
}

/* 总结评语样式 */
.summary-section {
  background: #F8F9FA;
  border: 1px solid #BCC1CA;
  padding: 11px 17px;
  margin-bottom: 20px;
}

.summary-title {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-bottom: 0;
}

.summary-content {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-left: 63px;
  margin-top: 0;
}

/* 不足之处样式 */
.deficiency-item {
  background: #F8F9FA;
  border: 1px solid #BCC1CA;
  padding: 11px 17px;
  margin-bottom: 10px;
}

.deficiency-header {
  display: flex;
  align-items: center;
  gap: 29px;
  margin-bottom: 26px;
}

.deficiency-title {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.deficiency-penalty {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.deficiency-count {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.deficiency-detail {
  display: flex;
  gap: 19px;
  margin-bottom: 0;
}

.detail-label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.detail-value {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

/* 错误列表样式 */
.error-list {
  margin-left: 0;
}

.error-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.error-row:last-child {
  margin-bottom: 0;
}

.error-item {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  flex: 1;
  min-width: 74px;
}

/* 最后批改时间样式 */
.last-modified {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  padding: 10px 17px;
  margin-bottom: 20px;
}
</style>
