<template>
  <div class="essay-requirements-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 返回按钮和标题 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <img :src="arrowLeftIcon" alt="返回" />
      </div>
      <div class="page-title">作文要求</div>
    </div>

    <!-- 作文信息区域 -->
    <div class="essay-info-section">
      <div class="essay-tags">
        <span class="unit-tag">第三单元</span>
        <span class="type-tag">单元作文</span>
        <span class="category-tag">全命题</span>
      </div>
    </div>

    <!-- 作文详情 -->
    <div class="essay-details">
      <div class="detail-item">
        <span class="label">作文命题</span>
        <span class="value">我的植物朋友</span>
      </div>
      
      <div class="detail-item">
        <span class="label">字数要求</span>
        <span class="value">300字</span>
      </div>
      
      <div class="detail-item">
        <span class="label">总分</span>
        <span class="value">30分</span>
      </div>
      
      <div class="detail-item requirements">
        <span class="label">作文要求</span>
        <div class="requirements-text">
          选择一项自己做过的小实验（可以是科学课上的，也可以是自己在家尝试的），按照"实验准备—实验过程—实验结果"的顺序写下来。重点把实验过程写清楚，可以用上"先……接着……然后……最后……"等表示顺序的词语。
        </div>
      </div>
    </div>

    <!-- 上传作文区域 -->
    <div class="upload-section">
      <div class="upload-title">
        <span>上传作文</span>
        <div class="warning-icon">
          <img :src="warningIcon" alt="警告" />
        </div>
      </div>
      
      <!-- 上传区域 -->
      <div class="upload-grid">
        <div class="upload-item" v-for="(item, index) in uploadItems" :key="index" @click="handleUploadClick(index)">
          <div class="upload-box">
            <img v-if="!item.hasContent" :src="addCrossIcon" alt="添加文件" class="upload-placeholder-img" />
            <img v-else :src="getUploadIcon(index)" alt="已上传文件" class="uploaded-file-img" />
          </div>
          <div v-if="item.hasContent" class="remove-button" @click.stop="removeUpload(index)">
            <img :src="removeIcon" alt="删除" />
          </div>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <button class="submit-button" @click="submitEssay">
        提交
      </button>
    </div>
  </div>
</template>

<script>
import arrowLeftIcon from '../assets/images/arrow-left.svg'
import warningIcon from '../assets/images/warning.svg'
import addCrossIcon from '../assets/images/addcross.svg'
import removeIcon from '../assets/images/remove1.svg'
import upload1Icon from '../assets/images/upload1.svg'
import upload2Icon from '../assets/images/upload2.svg'
import upload3Icon from '../assets/images/upload3.svg'
import upload4Icon from '../assets/images/upload4.svg'

export default {
  name: 'EssayRequirementsPage',
  emits: ['back'],
  data() {
    return {
      arrowLeftIcon,
      warningIcon,
      addCrossIcon,
      removeIcon,
      uploadIcons: [upload1Icon, upload2Icon, upload3Icon, upload4Icon],
      uploadItems: [
        { hasContent: false },
        { hasContent: false },
        { hasContent: false },
        { hasContent: false }
      ]
    }
  },
  methods: {
    goBack() {
      this.$emit('back')
    },
    submitEssay() {
      console.log('提交作文')
      // 这里可以添加提交逻辑
    },
    handleUploadClick(index) {
      console.log('点击上传框:', index)
      // 模拟上传文件
      this.uploadItems[index].hasContent = true
    },
    removeUpload(index) {
      console.log('删除上传文件:', index)
      this.uploadItems[index].hasContent = false
    },
    getUploadIcon(index) {
      return this.uploadIcons[index] || this.uploadIcons[0]
    }
  }
}
</script>

<style scoped>
.essay-requirements-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) { height: 3.4px; }
.bar:nth-child(2) { height: 5.53px; }
.bar:nth-child(3) { height: 8.51px; }
.bar:nth-child(4) { height: 10.21px; }

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 头部 */
.header {
  display: flex;
  align-items: center;
  padding: 8px 24px;
  position: relative;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  font-weight: 400;
}

/* 作文信息区域 */
.essay-info-section {
  background: #F8F9FA;
  padding: 8px 26px;
}

.essay-tags {
  display: flex;
  gap: 12px;
}

.unit-tag, .type-tag, .category-tag {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

/* 作文详情 */
.essay-details {
  padding: 18px 26px;
}

.detail-item {
  display: flex;
  margin-bottom: 26px;
}

.detail-item.requirements {
  flex-direction: column;
  align-items: flex-start;
}

.label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  width: 44px;
  flex-shrink: 0;
  margin-right: 12px;
}

.value {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.requirements-text {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  margin-top: 0;
  margin-left: 56px;
  max-width: 263px;
}

/* 上传区域 */
.upload-section {
  background: #F8F9FA;
  padding: 20px 24px;
}

.upload-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 13px;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.warning-icon {
  width: 16px;
  height: 16px;
}

.upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.upload-item {
  position: relative;
}

.upload-box {
  width: 131px;
  height: 76px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  cursor: pointer;
  border: none;
}

.upload-placeholder-img {
  width: 131px;
  height: 76px;
  object-fit: contain;
}

.uploaded-file-img {
  width: 131px;
  height: 76px;
  object-fit: contain;
}

.remove-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.remove-button img {
  width: 100%;
  height: 100%;
}

/* 提交按钮 */
.submit-section {
  padding: 20px 12px;
}

.submit-button {
  width: 350px;
  height: 52px;
  background: #636AE8;
  border: none;
  border-radius: 16px;
  color: #FFFFFF;
  font-size: 18px;
  line-height: 28px;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
}

.submit-button:hover {
  background: #5258d6;
}
</style>
