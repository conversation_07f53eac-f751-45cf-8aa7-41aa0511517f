<script setup>
import { ref } from 'vue'
import LoginPage from './components/LoginPage.vue'
import SmsLoginPage from './components/SmsLoginPage.vue'
import RoleSelectionPage from './components/RoleSelectionPage.vue'
import SuccessPage from './components/SuccessPage.vue'
import HomePage from './components/HomePage.vue'
import EssayRequirementsPage from './components/EssayRequirementsPage.vue'
import EssayReviewPage from './components/EssayReviewPage.vue'

const currentPage = ref('essay-review') // 'login', 'sms-login', 'role-selection', 'success', 'home', 'essay-requirements', 'essay-review'

const showSmsLogin = () => {
  currentPage.value = 'sms-login'
}

const showLogin = () => {
  currentPage.value = 'login'
}

const showRoleSelection = () => {
  currentPage.value = 'role-selection'
}

const showSuccess = () => {
  currentPage.value = 'success'
}

const showHome = () => {
  currentPage.value = 'home'
}

const showEssayRequirements = () => {
  currentPage.value = 'essay-requirements'
}

const showEssayReview = () => {
  currentPage.value = 'essay-review'
}

const handleRoleSelected = (role) => {
  console.log('用户选择的身份:', role)
  // 显示成功页面，然后跳转到首页
  showSuccess()
  setTimeout(() => {
    showHome()
  }, 2000) // 2秒后自动跳转到首页
}

const handleReturn = () => {
  // 从成功页面返回到登录页面
  showLogin()
}
</script>

<template>
  <div id="app">
    <LoginPage
      v-if="currentPage === 'login'"
      @sms-login="showSmsLogin"
    />
    <SmsLoginPage
      v-if="currentPage === 'sms-login'"
      @back="showLogin"
      @login-success="showRoleSelection"
    />
    <RoleSelectionPage
      v-if="currentPage === 'role-selection'"
      @back="showSmsLogin"
      @role-selected="handleRoleSelected"
    />
    <SuccessPage
      v-if="currentPage === 'success'"
      @return="handleReturn"
    />
    <HomePage
      v-if="currentPage === 'home'"
      @upload-essay="showEssayRequirements"
    />
    <EssayRequirementsPage
      v-if="currentPage === 'essay-requirements'"
      @back="showHome"
    />
    <EssayReviewPage
      v-if="currentPage === 'essay-review'"
      @back="showHome"
    />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

#app {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
</style>
